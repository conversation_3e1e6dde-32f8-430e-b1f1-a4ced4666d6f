# Consecutive Check-in Daily Reset Fix

## Problem Description

The `CONSECUTIVE_CHECKIN_CONFIGURABLE` task type was not being reset daily, preventing users from checking in more than once. This was a critical bug that broke the core functionality of consecutive check-in tasks.

### Root Cause

1. **Missing ResetPeriod**: Tasks created via `CreateConsecutiveCheckinTask` resolver were not assigned a `ResetPeriod`
2. **Daily Reset Query**: The `GetTasksNeedingReset(ctx, model.ResetDaily)` method only returns tasks with `reset_period = 'DAILY'`
3. **Unreachable Logic**: Although special reset logic existed for `CONSECUTIVE_CHECKIN_CONFIGURABLE` tasks in `ResetDailyTasks` method (lines 612-637), it was never executed because the tasks weren't returned by the query

### Impact

- Users could only check-in **once** for consecutive check-in tasks
- After the first check-in, task status remained `COMPLETED` or `CLAIMED`
- No daily reset occurred, breaking the consecutive check-in functionality
- The two existing jobs (`activity_cashback_daily_reset` and `activity_cashback_streak_monitoring`) did **not** solve this issue

## Solution

### Code Changes

**File**: `internal/controller/admin/graphql/resolvers/admin_activity_cashback.go`

```go
// Before (lines 220-231)
task := &model.ActivityTask{
    CategoryID:     uint(categoryID),
    Name:           input.Name.En,
    NameData:       multilingualName,
    Frequency:      model.FrequencyProgressive,
    TaskIdentifier: &taskIdentifier,
    Points:         0,
    Conditions:     conditions,
    SortOrder:      0,
    IsActive:       true,
}

// After (lines 221-233)
resetPeriod := model.ResetDaily // Consecutive check-in tasks need daily reset to allow new check-ins
task := &model.ActivityTask{
    CategoryID:     uint(categoryID),
    Name:           input.Name.En,
    NameData:       multilingualName,
    Frequency:      model.FrequencyProgressive,
    TaskIdentifier: &taskIdentifier,
    Points:         0,
    ResetPeriod:    &resetPeriod, // Enable daily reset for consecutive check-in functionality
    Conditions:     conditions,
    SortOrder:      0,
    IsActive:       true,
}
```

### How the Fix Works

1. **Task Creation**: `CONSECUTIVE_CHECKIN_CONFIGURABLE` tasks are now created with `ResetPeriod = DAILY`

2. **Daily Reset Inclusion**: Tasks are now included in `GetTasksNeedingReset(ctx, model.ResetDaily)` query

3. **Special Reset Logic**: The existing special reset logic in `ResetDailyTasks` method now executes:
   ```go
   // Special reset logic for configurable consecutive checkin tasks
   if progress.Task.TaskIdentifier != nil &&
       *progress.Task.TaskIdentifier == model.TaskIDConsecutiveCheckinConfigurable {
       
       // Reset status to allow new checkin, but preserve streak and progressValue
       if progress.Status == model.TaskStatusCompleted || progress.Status == model.TaskStatusClaimed {
           now := time.Now()
           progress.Status = model.TaskStatusNotStarted
           progress.LastResetAt = &now
           progress.UpdatedAt = now
           // Keep: StreakCount, ProgressValue, CompletionCount, PointsEarned
       }
   }
   ```

4. **Daily Flow**: 
   - **00:00 UTC**: Daily reset job runs
   - **Status Reset**: `COMPLETED`/`CLAIMED` → `NOT_STARTED`
   - **Preserved Data**: Streak, progress, completion count, and points earned are preserved
   - **New Check-in**: Users can check-in again for the new day

## Testing

### Test Coverage

Created comprehensive test suite in `internal/service/activity_cashback/consecutive_checkin_daily_reset_test.go`:

1. **Task_Created_With_Daily_Reset_Period**: Verifies tasks are created with correct `ResetPeriod`
2. **Daily_Reset_Logic_Preserves_Streak**: Confirms reset preserves important data
3. **Task_Should_Be_Included_In_Daily_Reset**: Validates query inclusion logic
4. **Fix_Resolves_Original_Problem**: End-to-end verification of the fix

### Test Results

```bash
=== RUN   TestConsecutiveCheckinDailyReset
=== RUN   TestConsecutiveCheckinDailyReset/Task_Created_With_Daily_Reset_Period
=== RUN   TestConsecutiveCheckinDailyReset/Daily_Reset_Logic_Preserves_Streak
=== RUN   TestConsecutiveCheckinDailyReset/Task_Should_Be_Included_In_Daily_Reset
=== RUN   TestConsecutiveCheckinDailyReset/Fix_Resolves_Original_Problem
--- PASS: TestConsecutiveCheckinDailyReset (0.00s)
```

All existing consecutive check-in tests continue to pass, confirming no regression.

## Deployment Notes

### Existing Tasks

- **New Tasks**: Will automatically have `ResetPeriod = DAILY` and work correctly
- **Existing Tasks**: May need manual database update to add `reset_period = 'DAILY'` if they exist

### Database Migration (if needed)

```sql
-- Update existing CONSECUTIVE_CHECKIN_CONFIGURABLE tasks to have daily reset
UPDATE activity_tasks 
SET reset_period = 'DAILY' 
WHERE task_identifier = 'CONSECUTIVE_CHECKIN_CONFIGURABLE' 
AND reset_period IS NULL;
```

### Verification

After deployment, verify:
1. New consecutive check-in tasks have `reset_period = 'DAILY'`
2. Daily reset job includes these tasks in processing
3. Users can check-in daily while preserving streaks

## Related Jobs

### activity_cashback_daily_reset (00:00 UTC)
- **Purpose**: Resets daily tasks including consecutive check-in tasks
- **Now Works**: With this fix, consecutive check-in tasks are properly included and reset

### activity_cashback_streak_monitoring (00:05 UTC)  
- **Purpose**: Monitors and resets broken streaks (when users miss days)
- **Unchanged**: Still handles streak breaks, but doesn't solve the daily reset issue

## Business Impact

### Before Fix
- ❌ Users could only check-in once ever
- ❌ Consecutive check-in feature was broken
- ❌ No daily engagement possible

### After Fix
- ✅ Users can check-in daily
- ✅ Streaks are properly maintained
- ✅ Milestone progression works correctly
- ✅ Daily engagement restored

## Conclusion

This fix resolves the critical issue with consecutive check-in tasks by ensuring they are properly included in the daily reset process. The solution is minimal, targeted, and preserves all existing functionality while enabling the intended daily check-in behavior.
