package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// TestConsecutiveCheckinConfigurableUnitTests tests the CONSECUTIVE_CHECKIN_CONFIGURABLE task type logic
func TestConsecutiveCheckinConfigurableUnitTests(t *testing.T) {
	// Setup test logger
	if global.GVA_LOG == nil {
		logger, _ := zap.NewDevelopment()
		global.GVA_LOG = logger
	}

	t.Run("Task_Setup_Configuration", func(t *testing.T) {
		// Test task configuration matches the GraphQL mutation requirements
		taskIdentifier := model.TaskIDConsecutiveCheckinConfigurable
		resetPeriod := model.ResetDaily
		verificationMethod := model.VerificationAuto

		task := &model.ActivityTask{
			Name:               "Henry Test: Continuous check-ins",
			Frequency:          model.FrequencyProgressive,
			TaskIdentifier:     &taskIdentifier,
			Points:             0, // Points determined by milestones
			ResetPeriod:        &resetPeriod,
			VerificationMethod: &verificationMethod,
			Conditions: &model.TaskConditions{
				ConsecutiveCheckinMilestones: []model.ConsecutiveCheckinMilestone{
					{Days: 3, Points: 10},
					{Days: 7, Points: 50},
					{Days: 30, Points: 100},
				},
			},
			IsActive: true,
		}

		// Verify task configuration
		assert.Equal(t, model.TaskIDConsecutiveCheckinConfigurable, *task.TaskIdentifier)
		assert.Equal(t, model.FrequencyProgressive, task.Frequency)
		assert.Equal(t, model.ResetDaily, *task.ResetPeriod)
		assert.Equal(t, 0, task.Points)
		assert.Len(t, task.Conditions.ConsecutiveCheckinMilestones, 3)

		// Verify milestone configuration
		milestones := task.Conditions.ConsecutiveCheckinMilestones
		assert.Equal(t, 3, milestones[0].Days)
		assert.Equal(t, 10, milestones[0].Points)
		assert.Equal(t, 7, milestones[1].Days)
		assert.Equal(t, 50, milestones[1].Points)
		assert.Equal(t, 30, milestones[2].Days)
		assert.Equal(t, 100, milestones[2].Points)
	})

	t.Run("API_Integration_Logic", func(t *testing.T) {
		// Test the CompleteTask API logic with mock service
		userID := uuid.New()
		taskID := uuid.New()

		// Create task with milestones
		task := &model.ActivityTask{
			ID:         taskID,
			CategoryID: 1,
			Name:       "Consecutive Check-in",
			Frequency:  model.FrequencyProgressive,
			Conditions: &model.TaskConditions{
				ConsecutiveCheckinMilestones: []model.ConsecutiveCheckinMilestone{
					{Days: 3, Points: 10},
					{Days: 7, Points: 50},
					{Days: 30, Points: 100},
				},
			},
		}

		// Test Day 1: First check-in (0/3 → 1/3 COMPLETED)
		initialProgress := &model.UserTaskProgress{
			ID:              uuid.New(),
			UserID:          userID,
			TaskID:          taskID,
			Status:          model.TaskStatusNotStarted,
			ProgressValue:   0,
			CompletionCount: 0,
			StreakCount:     0,
		}

		updatedProgress := &model.UserTaskProgress{
			ID:              initialProgress.ID,
			UserID:          userID,
			TaskID:          taskID,
			Status:          model.TaskStatusCompleted,
			ProgressValue:   1,
			CompletionCount: 0,
			StreakCount:     1,
		}

		// Setup mock service
		service := &MockActivityCashbackService{}
		service.On("UpdateActivity", mock.Anything, userID).Return(nil)
		service.On("GetTaskProgress", mock.Anything, userID, taskID).Return(initialProgress, nil).Once()
		service.On("UpdateStreak", mock.Anything, userID, taskID, true).Return(nil)
		service.On("GetTaskProgress", mock.Anything, userID, taskID).Return(updatedProgress, nil).Once()
		service.On("UpdateConsecutiveCheckinProgress", mock.Anything, userID, taskID, 1, 3, model.TaskStatusCompleted, 0).Return(nil)

		// Create handler and execute
		handler := NewConsecutiveCheckinConfigurableHandler(service)
		err := handler.Handle(context.Background(), userID, task, map[string]interface{}{})

		// Verify results
		assert.NoError(t, err)
		service.AssertExpectations(t)
	})

	t.Run("Continuous_Checkin_Milestone_Logic", func(t *testing.T) {
		// Test milestone progression logic
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 3, Points: 10},
			{Days: 7, Points: 50},
			{Days: 30, Points: 100},
		}

		service := &MockActivityCashbackService{}
		handler := NewConsecutiveCheckinConfigurableHandler(service)

		// Test milestone 1 targeting (CompletionCount = 0)
		progress1 := &model.UserTaskProgress{
			CompletionCount: 0,
			StreakCount:     0,
		}
		target1 := handler.getCurrentTargetMilestone(progress1, milestones)
		assert.NotNil(t, target1)
		assert.Equal(t, 3, target1.Days)
		assert.Equal(t, 10, target1.Points)

		// Test milestone 2 targeting (CompletionCount = 1)
		progress2 := &model.UserTaskProgress{
			CompletionCount: 1,
			StreakCount:     3,
		}
		target2 := handler.getCurrentTargetMilestone(progress2, milestones)
		assert.NotNil(t, target2)
		assert.Equal(t, 7, target2.Days)
		assert.Equal(t, 50, target2.Points)

		// Test milestone 3 targeting (CompletionCount = 2)
		progress3 := &model.UserTaskProgress{
			CompletionCount: 2,
			StreakCount:     7,
		}
		target3 := handler.getCurrentTargetMilestone(progress3, milestones)
		assert.NotNil(t, target3)
		assert.Equal(t, 30, target3.Days)
		assert.Equal(t, 100, target3.Points)

		// Test final milestone completion
		finalProgress := &model.UserTaskProgress{
			CompletionCount: 3,
			StreakCount:     30,
			Status:          model.TaskStatusClaimed,
		}
		assert.True(t, handler.isTaskPermanentlyCompleted(finalProgress, milestones))
	})

	t.Run("Interrupted_Checkin_Reset_Logic", func(t *testing.T) {
		// Test reset logic when users miss consecutive check-ins
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 3, Points: 10},
			{Days: 7, Points: 50},
			{Days: 30, Points: 100},
		}

		service := &MockActivityCashbackService{}
		handler := NewConsecutiveCheckinConfigurableHandler(service)

		// Test reset to first milestone after missing days
		resetProgress := &model.UserTaskProgress{
			Status:          model.TaskStatusNotStarted,
			ProgressValue:   0,
			StreakCount:     0,
			CompletionCount: 0, // Should be reset to 0
		}

		targetAfterReset := handler.getCurrentTargetMilestone(resetProgress, milestones)
		assert.NotNil(t, targetAfterReset)
		assert.Equal(t, 3, targetAfterReset.Days) // Should target first milestone
		assert.Equal(t, 10, targetAfterReset.Points)
	})

	t.Run("Scheduled_Job_Logic", func(t *testing.T) {
		// Test daily reset logic preserves streak but resets status
		progress := &model.UserTaskProgress{
			Status:          model.TaskStatusCompleted,
			StreakCount:     5,
			ProgressValue:   2,
			CompletionCount: 1,
			PointsEarned:    10,
		}

		// Simulate daily reset logic
		if progress.Status == model.TaskStatusCompleted || progress.Status == model.TaskStatusClaimed {
			now := time.Now()
			progress.Status = model.TaskStatusNotStarted
			progress.LastResetAt = &now
			progress.UpdatedAt = now
			// Preserve: StreakCount, ProgressValue, CompletionCount, PointsEarned
		}

		// Verify reset behavior
		assert.Equal(t, model.TaskStatusNotStarted, progress.Status)
		assert.Equal(t, 5, progress.StreakCount)     // Preserved
		assert.Equal(t, 2, progress.ProgressValue)   // Preserved
		assert.Equal(t, 1, progress.CompletionCount) // Preserved
		assert.Equal(t, 10, progress.PointsEarned)   // Preserved
		assert.NotNil(t, progress.LastResetAt)
	})

	t.Run("Error_Detection_Edge_Cases", func(t *testing.T) {
		// Test timezone handling
		now := time.Now()
		yesterday := now.Add(-24 * time.Hour)

		progress := &model.UserTaskProgress{
			LastCompletedAt: &yesterday,
		}

		// Verify timestamp is within expected range
		assert.NotNil(t, progress.LastCompletedAt)
		assert.True(t, progress.LastCompletedAt.Before(now))

		// Test invalid milestone configuration handling
		invalidMilestones := []model.ConsecutiveCheckinMilestone{
			{Days: 3, Points: 10},
			{Days: 3, Points: 20}, // Duplicate days
		}

		service := &MockActivityCashbackService{}
		handler := NewConsecutiveCheckinConfigurableHandler(service)

		// Should handle gracefully without crashing
		testProgress := &model.UserTaskProgress{CompletionCount: 0}
		target := handler.getCurrentTargetMilestone(testProgress, invalidMilestones)
		assert.NotNil(t, target) // Should still return a target

		// Test database consistency during milestone transitions
		consistencyMilestones := []model.ConsecutiveCheckinMilestone{
			{Days: 3, Points: 10},
			{Days: 7, Points: 50},
		}

		// Verify milestone progression consistency
		for day := 1; day <= 3; day++ {
			dayProgress := &model.UserTaskProgress{
				StreakCount:     day,
				CompletionCount: 0,
			}

			target := handler.getCurrentTargetMilestone(dayProgress, consistencyMilestones)
			assert.NotNil(t, target)
			assert.Equal(t, 3, target.Days) // Should always target first milestone until completed

			if day == 3 {
				// At milestone completion, should be ready to transition
				assert.Equal(t, target.Days, dayProgress.StreakCount)
			}
		}
	})

	t.Run("30_Day_Milestone_Processing", func(t *testing.T) {
		// Test the complete 30-day milestone scenario
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 3, Points: 10},
			{Days: 7, Points: 50},
			{Days: 30, Points: 100},
		}

		service := &MockActivityCashbackService{}
		handler := NewConsecutiveCheckinConfigurableHandler(service)

		// Test progression through all milestones
		testCases := []struct {
			day             int
			completionCount int
			expectedTarget  int
			expectedPoints  int
		}{
			{1, 0, 3, 10},    // Working towards milestone 1
			{3, 0, 3, 10},    // Reaching milestone 1
			{4, 1, 7, 50},    // Working towards milestone 2
			{7, 1, 7, 50},    // Reaching milestone 2
			{15, 2, 30, 100}, // Working towards milestone 3
			{30, 2, 30, 100}, // Reaching final milestone
		}

		for _, tc := range testCases {
			testProgress := &model.UserTaskProgress{
				StreakCount:     tc.day,
				CompletionCount: tc.completionCount,
			}

			target := handler.getCurrentTargetMilestone(testProgress, milestones)
			assert.NotNil(t, target, "Day %d should have a target", tc.day)
			assert.Equal(t, tc.expectedTarget, target.Days, "Day %d should target %d days", tc.day, tc.expectedTarget)
			assert.Equal(t, tc.expectedPoints, target.Points, "Day %d should have %d points", tc.day, tc.expectedPoints)

			// Check if milestone is reached
			if tc.day == target.Days {
				// Milestone reached - should award points
				assert.Equal(t, tc.expectedPoints, target.Points)
			}
		}

		// Test final milestone completion
		finalProgress := &model.UserTaskProgress{
			StreakCount:     30,
			CompletionCount: 3,
			Status:          model.TaskStatusClaimed,
		}
		assert.True(t, handler.isTaskPermanentlyCompleted(finalProgress, milestones))
	})
}
