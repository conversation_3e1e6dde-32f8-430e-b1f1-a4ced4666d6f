package activity_cashback

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestConsecutiveCheckinDailyReset tests that CONSECUTIVE_CHECKIN_CONFIGURABLE tasks are properly reset daily
func TestConsecutiveCheckinDailyReset(t *testing.T) {
	t.Run("Task_Created_With_Daily_Reset_Period", func(t *testing.T) {
		// Test that consecutive check-in tasks are created with ResetPeriod = DAILY
		// This ensures they will be included in the daily reset job

		// Create a mock task as it would be created by the resolver
		taskIdentifier := model.TaskIDConsecutiveCheckinConfigurable
		resetPeriod := model.ResetDaily

		task := &model.ActivityTask{
			ID:             uuid.New(),
			CategoryID:     1,
			Name:           "Daily Consecutive Check-in",
			Frequency:      model.FrequencyProgressive,
			TaskIdentifier: &taskIdentifier,
			Points:         0,
			ResetPeriod:    &resetPeriod, // This is the key fix
			Conditions: &model.TaskConditions{
				ConsecutiveCheckinMilestones: []model.ConsecutiveCheckinMilestone{
					{Days: 1, Points: 10},
					{Days: 2, Points: 50},
					{Days: 3, Points: 100},
				},
			},
			IsActive: true,
		}

		// Verify the task has the correct reset period
		require.NotNil(t, task.ResetPeriod, "ResetPeriod should not be nil")
		assert.Equal(t, model.ResetDaily, *task.ResetPeriod, "ResetPeriod should be DAILY")
		assert.Equal(t, model.TaskIDConsecutiveCheckinConfigurable, *task.TaskIdentifier, "TaskIdentifier should be CONSECUTIVE_CHECKIN_CONFIGURABLE")
	})

	t.Run("Daily_Reset_Logic_Preserves_Streak", func(t *testing.T) {
		// Test that the daily reset logic preserves streak and progress but resets status
		userID := uuid.New()
		taskID := uuid.New()

		// Create a progress record as if user completed check-in yesterday
		yesterday := time.Now().Add(-24 * time.Hour)
		progress := &model.UserTaskProgress{
			ID:              uuid.New(),
			UserID:          userID,
			TaskID:          taskID,
			Status:          model.TaskStatusCompleted, // User completed check-in
			ProgressValue:   1,                         // Progress towards current milestone
			StreakCount:     5,                         // User has a 5-day streak
			CompletionCount: 2,                         // User has completed 2 milestones
			PointsEarned:    60,                        // Total points earned (10 + 50)
			LastCompletedAt: &yesterday,
			LastResetAt:     &yesterday,
		}

		// Simulate the daily reset logic for consecutive check-in tasks
		// This is what happens in ResetDailyTasks method (lines 612-637)
		if progress.Status == model.TaskStatusCompleted || progress.Status == model.TaskStatusClaimed {
			now := time.Now()
			progress.Status = model.TaskStatusNotStarted // Reset status to allow new check-in
			progress.LastResetAt = &now
			progress.UpdatedAt = now
			// Keep: StreakCount, ProgressValue, CompletionCount, PointsEarned
		}

		// Verify that the reset preserves important data but allows new check-in
		assert.Equal(t, model.TaskStatusNotStarted, progress.Status, "Status should be reset to NOT_STARTED")
		assert.Equal(t, 5, progress.StreakCount, "StreakCount should be preserved")
		assert.Equal(t, 1, progress.ProgressValue, "ProgressValue should be preserved")
		assert.Equal(t, 2, progress.CompletionCount, "CompletionCount should be preserved")
		assert.Equal(t, 60, progress.PointsEarned, "PointsEarned should be preserved")
		assert.NotNil(t, progress.LastResetAt, "LastResetAt should be updated")
	})

	t.Run("Task_Should_Be_Included_In_Daily_Reset", func(t *testing.T) {
		// Test that a task with ResetPeriod = DAILY and proper last_reset_at timing
		// would be included in the GetTasksNeedingReset query

		userID := uuid.New()
		taskID := uuid.New()
		taskIdentifier := model.TaskIDConsecutiveCheckinConfigurable
		resetPeriod := model.ResetDaily

		// Create task with DAILY reset period
		task := &model.ActivityTask{
			ID:             taskID,
			CategoryID:     1,
			Name:           "Daily Consecutive Check-in",
			Frequency:      model.FrequencyProgressive,
			TaskIdentifier: &taskIdentifier,
			ResetPeriod:    &resetPeriod,
			IsActive:       true,
		}

		// Create progress that was last reset yesterday (should be reset today)
		yesterday := time.Now().Add(-24 * time.Hour).Truncate(24 * time.Hour)
		progress := &model.UserTaskProgress{
			ID:          uuid.New(),
			UserID:      userID,
			TaskID:      taskID,
			Status:      model.TaskStatusCompleted,
			LastResetAt: &yesterday,
			Task:        *task,
		}

		// Test the ShouldReset logic
		shouldReset := progress.ShouldReset(model.ResetDaily)
		assert.True(t, shouldReset, "Task should be reset when last reset was yesterday")

		// Test that task would be included in daily reset query
		// The query condition is: DATE_TRUNC('day', last_reset_at AT TIME ZONE 'UTC') < DATE_TRUNC('day', NOW() AT TIME ZONE 'UTC')
		now := time.Now().UTC().Truncate(24 * time.Hour)
		lastResetDay := yesterday.UTC().Truncate(24 * time.Hour)

		assert.True(t, lastResetDay.Before(now), "Last reset day should be before today")
	})

	t.Run("Fix_Resolves_Original_Problem", func(t *testing.T) {
		// Test that the fix resolves the original problem:
		// User can now check-in daily because status gets reset

		// Day 1: User checks in
		userID := uuid.New()
		taskID := uuid.New()

		// After first check-in
		day1 := time.Now().Add(-24 * time.Hour)
		progressAfterDay1 := &model.UserTaskProgress{
			UserID:          userID,
			TaskID:          taskID,
			Status:          model.TaskStatusCompleted, // User completed check-in
			StreakCount:     1,
			LastCompletedAt: &day1,
			LastResetAt:     &day1,
		}

		// Day 2: Daily reset job runs (this is the fix)
		// Status gets reset to NOT_STARTED while preserving streak
		now := time.Now()
		progressAfterDay1.Status = model.TaskStatusNotStarted
		progressAfterDay1.LastResetAt = &now

		// Day 2: User can now check-in again
		assert.Equal(t, model.TaskStatusNotStarted, progressAfterDay1.Status, "Status should allow new check-in")
		assert.Equal(t, 1, progressAfterDay1.StreakCount, "Streak should be preserved")

		// Simulate check-in logic in handler
		today := now.Truncate(24 * time.Hour)
		lastCheckIn := day1.Truncate(24 * time.Hour)
		yesterday := today.Add(-24 * time.Hour)

		// Handler logic: if last check-in was yesterday, this is consecutive
		isConsecutive := lastCheckIn.Equal(yesterday)
		assert.True(t, isConsecutive, "Check-in should be recognized as consecutive")

		// After Day 2 check-in: streak should increment
		expectedStreakAfterDay2 := 2
		assert.Equal(t, expectedStreakAfterDay2, progressAfterDay1.StreakCount+1, "Streak should increment to 2")
	})
}
