package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Base handler struct
type BaseTaskHandler struct {
	service    ActivityCashbackServiceInterface
	identifier model.TaskIdentifier
	category   string
}

// handleConsecutiveCheckin provides common logic for consecutive check-in tasks
func (h *BaseTaskHandler) handleConsecutiveCheckin(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}, targetDays int) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// Get current progress
	progress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if user already checked in today
	today := time.Now().Truncate(24 * time.Hour)
	lastCheckIn := time.Time{}
	if progress.LastCompletedAt != nil {
		lastCheckIn = progress.LastCompletedAt.Truncate(24 * time.Hour)
	}

	// If already checked in today, don't process again
	if lastCheckIn.Equal(today) {
		global.GVA_LOG.Debug("User already checked in today for consecutive task",
			zap.String("user_id", userID.String()),
			zap.String("task_id", task.ID.String()),
			zap.Time("last_checkin", lastCheckIn))
		return nil
	}

	// If last check-in was yesterday, increment streak
	yesterday := today.Add(-24 * time.Hour)
	if lastCheckIn.Equal(yesterday) {
		if err := h.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to update streak: %w", err)
		}
	} else if !lastCheckIn.IsZero() && !lastCheckIn.Equal(today) {
		// Reset ALL consecutive checkin tasks' streaks if gap > 1 day
		// This ensures that breaking the streak affects all consecutive tasks
		if err := h.resetAllConsecutiveStreaks(ctx, userID); err != nil {
			global.GVA_LOG.Error("Failed to reset all consecutive streaks", zap.Error(err))
		}
		// Start new streak
		if err := h.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to start new streak: %w", err)
		}
	} else {
		// First check-in ever, start streak
		if err := h.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to start first streak: %w", err)
		}
	}

	// Update progress normally - validation in CompleteTaskWithPoints will handle the rest
	if err := h.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
		return fmt.Errorf("failed to increment progress: %w", err)
	}

	// Check if milestone reached
	updatedProgress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get updated progress: %w", err)
	}

	if updatedProgress.StreakCount == targetDays {
		// Award bonus points for milestone and complete task
		bonusPoints := map[int]int{3: 50, 7: 200, 30: 1000}

		global.GVA_LOG.Info("Consecutive check-in milestone reached",
			zap.String("user_id", userID.String()),
			zap.Int("milestone", targetDays),
			zap.Int("bonus_points", bonusPoints[targetDays]))

		// Complete this progressive task - points will be awarded and progress preserved
		// Use CompleteTaskWithPoints which will handle both progress and points properly
		verificationData := map[string]interface{}{
			"milestone":    targetDays,
			"streak_count": updatedProgress.StreakCount,
			"method":       "consecutive_checkin",
			"processor":    "ConsecutiveCheckinHandler",
		}

		if err := h.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
			return fmt.Errorf("failed to complete progressive task: %w", err)
		}

		// Special handling for 30-day milestone - reset streak to start over from 3-day task
		if targetDays == 30 {
			if err := h.service.ResetStreak(ctx, userID, task.ID); err != nil {
				global.GVA_LOG.Error("Failed to reset streak after 30-day completion", zap.Error(err))
			}
			global.GVA_LOG.Info("30-day milestone completed, streak reset to start from 3-day task",
				zap.String("user_id", userID.String()))
		}
		// For 3-day and 7-day milestones, preserve streak to allow progression to next level
		// The streak will continue and user will see the next milestone task

		global.GVA_LOG.Info("Consecutive check-in milestone completed, next task will be available based on user progress",
			zap.String("user_id", userID.String()),
			zap.Int("completed_milestone", targetDays),
			zap.Int("current_streak", updatedProgress.StreakCount))
	}

	return nil
}

// activateNextMilestoneTask is deprecated - consecutive tasks are now filtered per-user
// based on their current streak, no need for global activation
func (h *BaseTaskHandler) activateNextMilestoneTask(ctx context.Context, userID uuid.UUID, completedDays int) error {
	// This method is no longer needed as task visibility is handled per-user
	// in the repository layer based on current streak
	global.GVA_LOG.Info("Consecutive check-in milestone completed, next task will be available based on user progress",
		zap.String("user_id", userID.String()),
		zap.Int("completed_milestone", completedDays))
	return nil
}

// activateTask sets a task as active
func (h *BaseTaskHandler) activateTask(ctx context.Context, taskID uuid.UUID) error {
	// Get the task
	task, err := h.service.GetTaskByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Set as active
	task.IsActive = true
	if err := h.service.UpdateTask(ctx, task); err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	global.GVA_LOG.Info("Task activated",
		zap.String("task_id", taskID.String()),
		zap.String("task_name", task.Name))

	return nil
}

// resetAllConsecutiveStreaks resets streaks for all consecutive checkin tasks
func (h *BaseTaskHandler) resetAllConsecutiveStreaks(ctx context.Context, userID uuid.UUID) error {
	// Get all consecutive checkin tasks
	consecutiveTaskIdentifiers := []model.TaskIdentifier{
		model.TaskIDConsecutiveCheckinConfigurable,
	}

	for _, identifier := range consecutiveTaskIdentifiers {
		// Find task by identifier
		tasks, err := h.service.GetTasksByCategory(ctx, model.CategoryDaily)
		if err != nil {
			continue
		}

		for _, task := range tasks {
			if task.TaskIdentifier != nil && *task.TaskIdentifier == identifier {
				// Reset streak for this task
				if err := h.service.ResetStreak(ctx, userID, task.ID); err != nil {
					global.GVA_LOG.Error("Failed to reset streak for consecutive task",
						zap.String("task_identifier", string(identifier)),
						zap.String("user_id", userID.String()),
						zap.Error(err))
				}
				break
			}
		}
	}

	global.GVA_LOG.Info("Reset all consecutive checkin streaks due to gap in login",
		zap.String("user_id", userID.String()))

	return nil
}

// Daily Task Handlers

// DailyCheckinHandler handles daily check-in tasks
type DailyCheckinHandler struct {
	BaseTaskHandler
}

func NewDailyCheckinHandler(service ActivityCashbackServiceInterface) *DailyCheckinHandler {
	return &DailyCheckinHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDDailyCheckin,
			category:   "daily",
		},
	}
}

func (h *DailyCheckinHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	global.GVA_LOG.Info("DailyCheckinHandler: Starting daily check-in process",
		zap.String("user_id", userID.String()),
		zap.String("task_id", task.ID.String()))

	// Update user activity
	global.GVA_LOG.Info("DailyCheckinHandler: Calling UpdateActivity", zap.String("user_id", userID.String()))
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err), zap.String("user_id", userID.String()))
	} else {
		global.GVA_LOG.Info("DailyCheckinHandler: UpdateActivity completed successfully", zap.String("user_id", userID.String()))
	}

	// Complete the task
	if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete daily check-in: %w", err)
	}

	global.GVA_LOG.Info("Daily check-in completed", zap.String("user_id", userID.String()))
	return nil
}

func (h *DailyCheckinHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *DailyCheckinHandler) GetCategory() string {
	return h.category
}

// MemeTradeHandler handles meme trade tasks
type MemeTradeHandler struct {
	BaseTaskHandler
}

func NewMemeTradeHandler(service ActivityCashbackServiceInterface) *MemeTradeHandler {
	return &MemeTradeHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDMemeTradeDaily,
			category:   "daily",
		},
	}
}

func (h *MemeTradeHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Check if task can be completed based on frequency
	progress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Auto-initialize task progress if user doesn't have it
			progress, err = h.service.InitializeTaskProgress(ctx, userID, task.ID)
			if err != nil {
				return fmt.Errorf("failed to initialize task progress: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get task progress: %w", err)
		}
	}

	// For daily tasks, check if already completed today
	global.GVA_LOG.Info("Checking task completion eligibility",
		zap.String("user_id", userID.String()),
		zap.String("task_id", task.ID.String()),
		zap.String("task_frequency", string(task.Frequency)),
		zap.Bool("has_last_completed", progress.LastCompletedAt != nil))

	if task.Frequency == model.FrequencyDaily && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		global.GVA_LOG.Info("Checking if MEME trade task already completed today",
			zap.String("user_id", userID.String()),
			zap.String("task_id", task.ID.String()),
			zap.Time("today", today),
			zap.Time("last_completed", lastCompleted),
			zap.Bool("already_completed", today.Equal(lastCompleted)))
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Warn("MEME trade task already completed today - skipping",
				zap.String("user_id", userID.String()),
				zap.String("task_id", task.ID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Task already completed today - this is expected business logic, not an error
		}
	}

	// Verify trade data
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid MEME trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "MEME" {
		return fmt.Errorf("invalid trade type for MEME task")
	}

	// Complete the task and award points
	verificationData := map[string]interface{}{
		"volume":     volume,
		"trade_type": tradeType,
		"method":     "automated_nats_event",
	}
	if err := h.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
		return fmt.Errorf("failed to complete MEME trade task with points: %w", err)
	}

	global.GVA_LOG.Info("MEME trade task completed", zap.String("user_id", userID.String()), zap.Float64("volume", volume))
	return nil
}

func (h *MemeTradeHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *MemeTradeHandler) GetCategory() string {
	return h.category
}

// PerpetualTradeHandler handles perpetual trade tasks
type PerpetualTradeHandler struct {
	BaseTaskHandler
}

func NewPerpetualTradeHandler(service ActivityCashbackServiceInterface) *PerpetualTradeHandler {
	return &PerpetualTradeHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDPerpetualTradeDaily,
			category:   "daily",
		},
	}
}

func (h *PerpetualTradeHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Check if task can be completed based on frequency
	progress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// For daily tasks, check if already completed today
	if task.Frequency == model.FrequencyDaily && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Debug("Perpetual trade task already completed today",
				zap.String("user_id", userID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Already completed today
		}
	}

	// Verify trade data
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid perpetual trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "PERPETUAL" {
		return fmt.Errorf("invalid trade type for perpetual task")
	}

	// Complete the task WITHOUT awarding points (points are awarded by TradingPointsHandler)
	verificationData := map[string]interface{}{
		"volume":     volume,
		"trade_type": tradeType,
		"method":     "automated_nats_event",
		"note":       "points_awarded_by_trading_points_handler",
	}
	if err := h.service.CompleteTask(ctx, userID, task.ID, verificationData); err != nil {
		return fmt.Errorf("failed to complete perpetual trade task: %w", err)
	}

	global.GVA_LOG.Info("Perpetual trade task completed", zap.String("user_id", userID.String()), zap.Float64("volume", volume))
	return nil
}

func (h *PerpetualTradeHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *PerpetualTradeHandler) GetCategory() string {
	return h.category
}

// MarketPageViewHandler handles market page view tasks
type MarketPageViewHandler struct {
	BaseTaskHandler
}

func NewMarketPageViewHandler(service ActivityCashbackServiceInterface) *MarketPageViewHandler {
	return &MarketPageViewHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDMarketPageView,
			category:   "daily",
		},
	}
}

func (h *MarketPageViewHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
	}

	// Complete the task
	if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete market check task: %w", err)
	}

	global.GVA_LOG.Info("Market check task completed", zap.String("user_id", userID.String()))
	return nil
}

func (h *MarketPageViewHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *MarketPageViewHandler) GetCategory() string {
	return h.category
}

// ConsecutiveCheckinConfigurableHandler handles configurable consecutive check-in tasks with multiple milestones
type ConsecutiveCheckinConfigurableHandler struct {
	BaseTaskHandler
}

func NewConsecutiveCheckinConfigurableHandler(service ActivityCashbackServiceInterface) *ConsecutiveCheckinConfigurableHandler {
	return &ConsecutiveCheckinConfigurableHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDConsecutiveCheckinConfigurable,
			category:   "daily",
		},
	}
}

func (h *ConsecutiveCheckinConfigurableHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// Get current progress
	progress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if user already checked in today
	today := time.Now().Truncate(24 * time.Hour)
	lastCheckIn := time.Time{}
	if progress.LastCompletedAt != nil {
		lastCheckIn = progress.LastCompletedAt.Truncate(24 * time.Hour)
	}

	// If already checked in today, don't process again
	if lastCheckIn.Equal(today) {
		global.GVA_LOG.Warn("User already checked in today for configurable consecutive task",
			zap.String("user_id", userID.String()),
			zap.String("task_id", task.ID.String()),
			zap.Time("last_checkin", lastCheckIn))
		return nil // Task already completed today - this is expected business logic, not an error
	}

	// Get milestones from task conditions - we need this for logic
	if task.Conditions == nil || len(task.Conditions.ConsecutiveCheckinMilestones) == 0 {
		return fmt.Errorf("consecutive check-in task has no milestones configured")
	}

	milestones := task.Conditions.ConsecutiveCheckinMilestones

	// Check if task is already permanently completed (reached final milestone)
	if h.isTaskPermanentlyCompleted(progress, milestones) {
		global.GVA_LOG.Info("Consecutive check-in task is permanently completed",
			zap.String("user_id", userID.String()),
			zap.String("task_id", task.ID.String()))
		return fmt.Errorf("task is permanently completed")
	}

	// Determine current target milestone based on progress
	currentTarget := h.getCurrentTargetMilestone(progress, milestones)
	if currentTarget == nil {
		return fmt.Errorf("unable to determine current target milestone")
	}

	// Handle streak logic
	yesterday := today.Add(-24 * time.Hour)
	if lastCheckIn.Equal(yesterday) {
		// Consecutive day - increment streak
		if err := h.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to update streak: %w", err)
		}
	} else if !lastCheckIn.IsZero() && !lastCheckIn.Equal(today) {
		// Gap in checkin - reset to start from milestone 1
		if err := h.resetToFirstMilestone(ctx, userID, task.ID, milestones[0]); err != nil {
			return fmt.Errorf("failed to reset to first milestone: %w", err)
		}
		// Start new streak
		if err := h.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to start new streak: %w", err)
		}
	} else {
		// First check-in ever, start streak
		if err := h.service.UpdateStreak(ctx, userID, task.ID, true); err != nil {
			return fmt.Errorf("failed to start first streak: %w", err)
		}
	}

	// Get updated progress after streak update
	updatedProgress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get updated progress: %w", err)
	}

	// Handle milestone completion logic
	currentStreak := updatedProgress.StreakCount

	// Determine status and points based on milestone completion
	var status model.TaskStatus
	var pointsToAward int

	// Check if current streak reaches the target milestone
	if currentStreak == currentTarget.Days {
		// Milestone reached - award points and set status to CLAIMED
		pointsToAward = currentTarget.Points
		status = model.TaskStatusClaimed

		// Award points to user
		reason := fmt.Sprintf("consecutive_checkin_milestone_%d_days", currentTarget.Days)
		if err := h.service.AddPoints(ctx, userID, pointsToAward, reason); err != nil {
			return fmt.Errorf("failed to award milestone points: %w", err)
		}

		global.GVA_LOG.Info("Consecutive check-in milestone completed",
			zap.String("user_id", userID.String()),
			zap.Int("milestone_days", currentTarget.Days),
			zap.Int("points_awarded", pointsToAward),
			zap.Int("current_streak", currentStreak))

		// Update progress with CLAIMED status
		if err := h.service.UpdateConsecutiveCheckinProgress(ctx, userID, task.ID, currentStreak, currentTarget.Days, status, pointsToAward); err != nil {
			return fmt.Errorf("failed to update consecutive check-in progress: %w", err)
		}

		// Check if this was the final milestone
		if h.isFinalMilestone(currentTarget, milestones) {
			global.GVA_LOG.Info("Final consecutive check-in milestone reached - task permanently completed",
				zap.String("user_id", userID.String()),
				zap.Int("final_milestone", currentTarget.Days))
			return nil // Task is permanently completed
		} else {
			// Immediately transition to next milestone after completion
			// This implements the progressive transition: 1/1 CLAIMED → 0/2 NOT_STARTED
			// Do this in a single atomic operation
			if err := h.completeAndTransitionToNextMilestone(ctx, userID, task.ID, milestones, *currentTarget, updatedProgress.CompletionCount+1, pointsToAward); err != nil {
				global.GVA_LOG.Error("Failed to complete and transition to next milestone", zap.Error(err))
				return fmt.Errorf("failed to complete and transition to next milestone: %w", err)
			}
			return nil // Exit early after transition
		}
	} else {
		// Progress made but milestone not reached - set status to COMPLETED
		status = model.TaskStatusCompleted
		pointsToAward = 0

		global.GVA_LOG.Info("Consecutive check-in progress updated",
			zap.String("user_id", userID.String()),
			zap.Int("current_streak", currentStreak),
			zap.Int("target_milestone", currentTarget.Days))

		// Update progress with COMPLETED status (no points awarded yet)
		if err := h.service.UpdateConsecutiveCheckinProgress(ctx, userID, task.ID, currentStreak, currentTarget.Days, status, pointsToAward); err != nil {
			return fmt.Errorf("failed to update consecutive check-in progress: %w", err)
		}
	}

	return nil
}

func (h *ConsecutiveCheckinConfigurableHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *ConsecutiveCheckinConfigurableHandler) GetCategory() string {
	return h.category
}

// Helper methods for ConsecutiveCheckinConfigurableHandler

// isTaskPermanentlyCompleted checks if the task has reached the final milestone and is permanently completed
func (h *ConsecutiveCheckinConfigurableHandler) isTaskPermanentlyCompleted(progress *model.UserTaskProgress, milestones []model.ConsecutiveCheckinMilestone) bool {
	if len(milestones) == 0 {
		return false
	}

	// Find the highest milestone
	finalMilestone := milestones[0]
	for _, milestone := range milestones {
		if milestone.Days > finalMilestone.Days {
			finalMilestone = milestone
		}
	}

	// Task is permanently completed if user has reached the final milestone and status is CLAIMED
	return progress.StreakCount >= finalMilestone.Days && progress.Status == model.TaskStatusClaimed
}

// getCurrentTargetMilestone determines the current target milestone based on user progress
// This implements the exact behavior pattern using CompletionCount to track completed milestones:
// - CompletionCount = 0: Working towards milestone 1
// - CompletionCount = 1: Working towards milestone 2
// - CompletionCount = 2: Working towards milestone 3
func (h *ConsecutiveCheckinConfigurableHandler) getCurrentTargetMilestone(progress *model.UserTaskProgress, milestones []model.ConsecutiveCheckinMilestone) *model.ConsecutiveCheckinMilestone {
	if len(milestones) == 0 {
		return nil
	}

	// Sort milestones by days (ascending)
	sortedMilestones := make([]model.ConsecutiveCheckinMilestone, len(milestones))
	copy(sortedMilestones, milestones)
	for i := 0; i < len(sortedMilestones)-1; i++ {
		for j := i + 1; j < len(sortedMilestones); j++ {
			if sortedMilestones[i].Days > sortedMilestones[j].Days {
				sortedMilestones[i], sortedMilestones[j] = sortedMilestones[j], sortedMilestones[i]
			}
		}
	}

	// Use CompletionCount to determine which milestone to work towards
	// CompletionCount tracks how many milestones have been completed
	targetMilestoneIndex := progress.CompletionCount

	// If we've completed all milestones, return the final milestone
	if targetMilestoneIndex >= len(sortedMilestones) {
		return &sortedMilestones[len(sortedMilestones)-1]
	}

	// Return the milestone at the target index
	return &sortedMilestones[targetMilestoneIndex]
}

// resetToFirstMilestone resets the task progress to start from the first milestone
func (h *ConsecutiveCheckinConfigurableHandler) resetToFirstMilestone(ctx context.Context, userID, taskID uuid.UUID, firstMilestone model.ConsecutiveCheckinMilestone) error {
	// Reset to first milestone using ResetStreak which will reset the streak count
	if err := h.service.ResetStreak(ctx, userID, taskID); err != nil {
		return fmt.Errorf("failed to reset streak: %w", err)
	}

	// Set progress to 0 using SetProgressWithoutAutoComplete
	if err := h.service.SetProgressWithoutAutoComplete(ctx, userID, taskID, 0); err != nil {
		return fmt.Errorf("failed to reset progress value: %w", err)
	}

	// Reset CompletionCount to 0 so user starts from milestone 1
	// This ensures getCurrentTargetMilestone returns the first milestone
	if err := h.service.ResetTaskCompletionCount(ctx, userID, taskID); err != nil {
		return fmt.Errorf("failed to reset completion count: %w", err)
	}

	global.GVA_LOG.Info("Reset consecutive check-in task to first milestone",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("target_milestone", firstMilestone.Days))

	return nil
}

// isFinalMilestone checks if the given milestone is the final (highest) milestone
func (h *ConsecutiveCheckinConfigurableHandler) isFinalMilestone(milestone *model.ConsecutiveCheckinMilestone, milestones []model.ConsecutiveCheckinMilestone) bool {
	if milestone == nil || len(milestones) == 0 {
		return false
	}

	// Find the highest milestone
	maxDays := milestone.Days
	for _, m := range milestones {
		if m.Days > maxDays {
			maxDays = m.Days
		}
	}

	return milestone.Days == maxDays
}

// completeAndTransitionToNextMilestone completes the current milestone and immediately transitions to the next one
// This implements the progressive transition: 1/1 CLAIMED → 0/2 NOT_STARTED in a single atomic operation
func (h *ConsecutiveCheckinConfigurableHandler) completeAndTransitionToNextMilestone(ctx context.Context, userID, taskID uuid.UUID, milestones []model.ConsecutiveCheckinMilestone, completedMilestone model.ConsecutiveCheckinMilestone, nextCompletionCount int, pointsAwarded int) error {
	// Sort milestones by days (ascending)
	sortedMilestones := make([]model.ConsecutiveCheckinMilestone, len(milestones))
	copy(sortedMilestones, milestones)
	for i := 0; i < len(sortedMilestones)-1; i++ {
		for j := i + 1; j < len(sortedMilestones); j++ {
			if sortedMilestones[i].Days > sortedMilestones[j].Days {
				sortedMilestones[i], sortedMilestones[j] = sortedMilestones[j], sortedMilestones[i]
			}
		}
	}

	// Find the next milestone after the completed one
	var nextMilestone *model.ConsecutiveCheckinMilestone
	for _, milestone := range sortedMilestones {
		if milestone.Days > completedMilestone.Days {
			nextMilestone = &milestone
			break
		}
	}

	if nextMilestone == nil {
		return fmt.Errorf("no next milestone found after %d days", completedMilestone.Days)
	}

	// Update progress directly to the next milestone state: 0/X NOT_STARTED
	// This skips the intermediate CLAIMED state and goes directly to the next milestone
	// Also set the completion count to reflect the completed milestone
	// Pass 0 for pointsEarned to prevent double-counting (points were already awarded above)
	if err := h.service.UpdateConsecutiveCheckinProgressWithCompletionCount(ctx, userID, taskID, 0, nextMilestone.Days, model.TaskStatusNotStarted, 0, nextCompletionCount); err != nil {
		return fmt.Errorf("failed to transition to next milestone: %w", err)
	}

	global.GVA_LOG.Info("Completed milestone and transitioned to next",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("completed_milestone_days", completedMilestone.Days),
		zap.Int("next_milestone_days", nextMilestone.Days),
		zap.Int("completion_count", nextCompletionCount))

	return nil
}

// transitionToNextMilestone immediately transitions the user to the next milestone after completing the current one
// This implements the progressive transition: 1/1 CLAIMED → 0/2 NOT_STARTED
func (h *ConsecutiveCheckinConfigurableHandler) transitionToNextMilestone(ctx context.Context, userID, taskID uuid.UUID, milestones []model.ConsecutiveCheckinMilestone, nextCompletionCount int) error {
	// Sort milestones by days (ascending)
	sortedMilestones := make([]model.ConsecutiveCheckinMilestone, len(milestones))
	copy(sortedMilestones, milestones)
	for i := 0; i < len(sortedMilestones)-1; i++ {
		for j := i + 1; j < len(sortedMilestones); j++ {
			if sortedMilestones[i].Days > sortedMilestones[j].Days {
				sortedMilestones[i], sortedMilestones[j] = sortedMilestones[j], sortedMilestones[i]
			}
		}
	}

	// Check if there's a next milestone
	if nextCompletionCount >= len(sortedMilestones) {
		// No more milestones, task is completed
		return nil
	}

	nextMilestone := sortedMilestones[nextCompletionCount]

	// Transition to next milestone: reset progress to 0, set new target, set status to NOT_STARTED
	// This creates the 0/X display immediately after milestone completion
	// Use the method that doesn't increment CompletionCount since it was already incremented
	if err := h.service.UpdateConsecutiveCheckinProgressWithoutCompletionIncrement(ctx, userID, taskID, 0, nextMilestone.Days, model.TaskStatusNotStarted, 0); err != nil {
		return fmt.Errorf("failed to transition to next milestone: %w", err)
	}

	global.GVA_LOG.Info("Transitioned to next milestone",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("next_milestone_days", nextMilestone.Days),
		zap.Int("completion_count", nextCompletionCount))

	return nil
}

// Trading Task Handlers

// TradingPointsHandler handles trading points tasks
type TradingPointsHandler struct {
	BaseTaskHandler
}

func NewTradingPointsHandler(service ActivityCashbackServiceInterface) *TradingPointsHandler {
	return &TradingPointsHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDTradingPoints,
			category:   "trading",
		},
	}
}

func (h *TradingPointsHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		global.GVA_LOG.Error("Invalid trading volume in TradingPointsHandler",
			zap.String("user_id", userID.String()),
			zap.Any("volume_raw", data["volume"]),
			zap.Bool("volume_ok", ok),
			zap.Float64("volume_parsed", volume))
		return fmt.Errorf("invalid trading volume")
	}

	// Get trade type to apply different weights
	tradeType, ok := data["trade_type"].(string)
	if !ok {
		tradeType = "MEME" // Default to MEME if not specified
	}

	global.GVA_LOG.Info("TradingPointsHandler processing",
		zap.String("user_id", userID.String()),
		zap.Float64("volume", volume),
		zap.String("trade_type", tradeType),
		zap.Any("all_data", data))

	// Process both MEME and PERPETUAL trading volume for Trading Points
	// Apply different calculation logic based on trade type
	var points int
	switch tradeType {
	case "MEME":
		// MEME trades: 1:1 volume with tier-based conversion
		weightedVolume := volume
		switch {
		case weightedVolume >= 10000:
			points = 40
		case weightedVolume >= 3000:
			points = 25
		case weightedVolume >= 500:
			points = 12
		case weightedVolume >= 100:
			points = 5
		case weightedVolume >= 1:
			points = 1
		default:
			points = 0
		}
	case "PERPETUAL":
		// PERPETUAL trades: direct point calculation (volume/95)
		// No tier-based conversion - use the calculated value directly
		directPoints := volume / 95.0
		points = int(directPoints) // Convert to integer points
		if points < 0 {
			points = 0
		}
	default:
		global.GVA_LOG.Info("Skipping unsupported trade type for Trading Points",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("volume", volume))
		return nil
	}

	if points > 0 {
		// Award points directly (not through task completion)
		if err := h.service.AddPoints(ctx, userID, points, fmt.Sprintf("trading_volume_%.2f", volume)); err != nil {
			return fmt.Errorf("failed to award trading points: %w", err)
		}

		// Update progress and points earned
		if err := h.service.IncrementProgressWithPoints(ctx, userID, task.ID, points, points); err != nil {
			return fmt.Errorf("failed to update trading points progress: %w", err)
		}

		global.GVA_LOG.Info("Trading points awarded",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("original_volume", volume),
			zap.Float64("calculated_value", func() float64 {
				if tradeType == "PERPETUAL" {
					return volume / 95.0
				}
				return volume
			}()),
			zap.String("calculation_method", func() string {
				if tradeType == "PERPETUAL" {
					return "direct_points"
				}
				return "tier_based"
			}()),
			zap.Int("points", points))
	}

	return nil
}

func (h *TradingPointsHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *TradingPointsHandler) GetCategory() string {
	return h.category
}

// AccumulatedTradingHandler handles accumulated trading volume tasks
type AccumulatedTradingHandler struct {
	BaseTaskHandler
	targetVolume float64
}

func NewAccumulatedTradingHandler(service ActivityCashbackServiceInterface, identifier model.TaskIdentifier, targetVolume float64) *AccumulatedTradingHandler {
	return &AccumulatedTradingHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: identifier,
			category:   "trading",
		},
		targetVolume: targetVolume,
	}
}

func (h *AccumulatedTradingHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// Get current progress to check if already completed
	progress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// Check if already completed
	if progress.IsCompleted() {
		return nil // Already completed
	}

	// Get user's accumulated trading volume from user_tier_info
	tierInfo, err := h.service.GetUserTierInfo(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user tier info: %w", err)
	}

	// Use effective volume (real-time accumulated volume if available, otherwise daily trading volume)
	effectiveVolume := tierInfo.GetEffectiveVolume()
	accumulatedVolume, _ := effectiveVolume.Float64()

	// Check if milestone reached
	if accumulatedVolume >= h.targetVolume {
		// Complete the task with points (this will set progress to milestone and award points)
		verificationData := map[string]interface{}{
			"milestone":          h.targetVolume,
			"accumulated_volume": accumulatedVolume,
			"method":             "automated_tier_info_check",
			"processor":          "AccumulatedTradingHandler",
			"source":             "user_tier_info.effective_volume",
		}
		if err := h.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
			return fmt.Errorf("failed to complete accumulated trading task with points: %w", err)
		}

		if global.GVA_LOG != nil {
			global.GVA_LOG.Info("Accumulated trading milestone reached via handler",
				zap.String("user_id", userID.String()),
				zap.Float64("milestone", h.targetVolume),
				zap.Float64("accumulated_volume", accumulatedVolume),
				zap.Int("points", task.Points))
		}
	} else {
		// Just update progress without completing
		progressValue := int(accumulatedVolume)
		if err := h.service.SetProgress(ctx, userID, task.ID, progressValue); err != nil {
			return fmt.Errorf("failed to update accumulated trading progress: %w", err)
		}

		if global.GVA_LOG != nil {
			global.GVA_LOG.Debug("Accumulated trading milestone not yet reached",
				zap.String("user_id", userID.String()),
				zap.Float64("milestone", h.targetVolume),
				zap.Float64("accumulated_volume", accumulatedVolume),
				zap.Float64("remaining", h.targetVolume-accumulatedVolume))
		}
	}

	return nil
}

func (h *AccumulatedTradingHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *AccumulatedTradingHandler) GetCategory() string {
	return h.category
}

// TradingMemeTradeHandler handles meme trade tasks in trading category
type TradingMemeTradeHandler struct {
	BaseTaskHandler
}

func NewTradingMemeTradeHandler(service ActivityCashbackServiceInterface) *TradingMemeTradeHandler {
	return &TradingMemeTradeHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDMemeTradeDaily,
			category:   "trading",
		},
	}
}

func (h *TradingMemeTradeHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// Check if task can be completed based on frequency
	progress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Auto-initialize task progress if user doesn't have it
			progress, err = h.service.InitializeTaskProgress(ctx, userID, task.ID)
			if err != nil {
				return fmt.Errorf("failed to initialize task progress: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get task progress: %w", err)
		}
	}

	// For daily tasks, check if already completed today
	global.GVA_LOG.Info("Checking Trading task completion eligibility",
		zap.String("user_id", userID.String()),
		zap.String("task_id", task.ID.String()),
		zap.String("task_frequency", string(task.Frequency)),
		zap.Bool("has_last_completed", progress.LastCompletedAt != nil))

	if task.Frequency == model.FrequencyDaily && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		global.GVA_LOG.Info("Checking if Trading MEME trade task already completed today",
			zap.String("user_id", userID.String()),
			zap.String("task_id", task.ID.String()),
			zap.Time("today", today),
			zap.Time("last_completed", lastCompleted),
			zap.Bool("already_completed", today.Equal(lastCompleted)))
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Warn("Trading MEME trade task already completed today - skipping",
				zap.String("user_id", userID.String()),
				zap.String("task_id", task.ID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Task already completed today - this is expected business logic, not an error
		}
	}

	// Verify trade data
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid MEME trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "MEME" {
		return fmt.Errorf("invalid trade type for MEME task")
	}

	// Complete the task and award points
	verificationData := map[string]interface{}{
		"volume":     volume,
		"trade_type": tradeType,
		"method":     "automated_nats_event",
		"category":   "trading",
	}
	if err := h.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
		return fmt.Errorf("failed to complete MEME trade task with points: %w", err)
	}

	global.GVA_LOG.Info("MEME trade task completed", zap.String("user_id", userID.String()), zap.Float64("volume", volume))
	return nil
}

func (h *TradingMemeTradeHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *TradingMemeTradeHandler) GetCategory() string {
	return h.category
}

// TradingPerpetualTradeHandler handles perpetual trade tasks in trading category
type TradingPerpetualTradeHandler struct {
	BaseTaskHandler
}

func NewTradingPerpetualTradeHandler(service ActivityCashbackServiceInterface) *TradingPerpetualTradeHandler {
	return &TradingPerpetualTradeHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDPerpetualTradeDaily,
			category:   "trading",
		},
	}
}

func (h *TradingPerpetualTradeHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// Check if task can be completed based on frequency
	progress, err := h.service.GetTaskProgress(ctx, userID, task.ID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	// For daily tasks, check if already completed today
	if task.Frequency == model.FrequencyDaily && progress.LastCompletedAt != nil {
		today := time.Now().Truncate(24 * time.Hour)
		lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
		if today.Equal(lastCompleted) {
			global.GVA_LOG.Debug("Perpetual trade task already completed today",
				zap.String("user_id", userID.String()),
				zap.Time("last_completed", *progress.LastCompletedAt))
			return nil // Already completed today
		}
	}

	// Verify trade data
	volume, ok := data["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid perpetual trade volume")
	}

	tradeType, ok := data["trade_type"].(string)
	if !ok || tradeType != "PERPETUAL" {
		return fmt.Errorf("invalid trade type for perpetual task")
	}

	// Complete the task WITHOUT awarding points (points are awarded by TradingPointsHandler)
	verificationData := map[string]interface{}{
		"volume":     volume,
		"trade_type": tradeType,
		"method":     "automated_nats_event",
		"category":   "trading",
		"note":       "points_awarded_by_trading_points_handler",
	}
	if err := h.service.CompleteTask(ctx, userID, task.ID, verificationData); err != nil {
		return fmt.Errorf("failed to complete perpetual trade task: %w", err)
	}

	global.GVA_LOG.Info("Perpetual trade task completed", zap.String("user_id", userID.String()), zap.Float64("volume", volume))
	return nil
}

func (h *TradingPerpetualTradeHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *TradingPerpetualTradeHandler) GetCategory() string {
	return h.category
}

// Community Task Handlers

// TwitterFollowHandler handles Twitter follow tasks
type TwitterFollowHandler struct {
	BaseTaskHandler
}

func NewTwitterFollowHandler(service ActivityCashbackServiceInterface) *TwitterFollowHandler {
	return &TwitterFollowHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDTwitterFollow,
			category:   "community",
		},
	}
}

func (h *TwitterFollowHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// For now, implement basic completion logic
	// TODO: Implement proper Twitter follow verification
	if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete Twitter follow task: %w", err)
	}
	return nil
}

func (h *TwitterFollowHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *TwitterFollowHandler) GetCategory() string {
	return h.category
}

// TwitterRetweetHandler handles Twitter retweet tasks
type TwitterRetweetHandler struct {
	BaseTaskHandler
}

func NewTwitterRetweetHandler(service ActivityCashbackServiceInterface) *TwitterRetweetHandler {
	return &TwitterRetweetHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDTwitterRetweet,
			category:   "community",
		},
	}
}

func (h *TwitterRetweetHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// For now, implement basic completion logic
	// TODO: Implement proper Twitter retweet verification
	if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete Twitter retweet task: %w", err)
	}
	return nil
}

func (h *TwitterRetweetHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *TwitterRetweetHandler) GetCategory() string {
	return h.category
}

// TwitterLikeHandler handles Twitter like tasks
type TwitterLikeHandler struct {
	BaseTaskHandler
}

func NewTwitterLikeHandler(service ActivityCashbackServiceInterface) *TwitterLikeHandler {
	return &TwitterLikeHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDTwitterLike,
			category:   "community",
		},
	}
}

func (h *TwitterLikeHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// For now, implement basic completion logic
	// TODO: Implement proper Twitter like verification
	if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete Twitter like task: %w", err)
	}
	return nil
}

func (h *TwitterLikeHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *TwitterLikeHandler) GetCategory() string {
	return h.category
}

// TelegramJoinHandler handles Telegram join tasks
type TelegramJoinHandler struct {
	BaseTaskHandler
}

func NewTelegramJoinHandler(service ActivityCashbackServiceInterface) *TelegramJoinHandler {
	return &TelegramJoinHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDTelegramJoin,
			category:   "community",
		},
	}
}

func (h *TelegramJoinHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// For now, implement basic completion logic
	// TODO: Implement proper Telegram join verification
	if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete Telegram join task: %w", err)
	}
	return nil
}

func (h *TelegramJoinHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *TelegramJoinHandler) GetCategory() string {
	return h.category
}

// InviteFriendsHandler handles invite friends tasks
type InviteFriendsHandler struct {
	BaseTaskHandler
}

func NewInviteFriendsHandler(service ActivityCashbackServiceInterface) *InviteFriendsHandler {
	return &InviteFriendsHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDInviteFriends,
			category:   "community",
		},
	}
}

func (h *InviteFriendsHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// This handler is now primarily triggered by first transaction events
	// The actual logic is handled in the affiliate service when FirstTransactionAt is updated

	// Verify that we have the required data
	invitedUserID, ok := data["invited_user_id"].(string)
	if !ok {
		return fmt.Errorf("invited_user_id not provided in data")
	}

	// Log the completion
	global.GVA_LOG.Info("Invite friends task completed via handler",
		zap.String("referrer_id", userID.String()),
		zap.String("invited_user_id", invitedUserID),
		zap.String("task_id", task.ID.String()))

	// Complete the task progress (this will create the completion record and award points)
	if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete invite friends task: %w", err)
	}

	return nil
}

func (h *InviteFriendsHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *InviteFriendsHandler) GetCategory() string {
	return h.category
}

// ShareReferralHandler handles share referral tasks
type ShareReferralHandler struct {
	BaseTaskHandler
}

func NewShareReferralHandler(service ActivityCashbackServiceInterface) *ShareReferralHandler {
	return &ShareReferralHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDShareReferral,
			category:   "community",
		},
	}
}

func (h *ShareReferralHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// For now, implement basic completion logic
	// TODO: Implement proper share referral verification
	if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
		return fmt.Errorf("failed to complete share referral task: %w", err)
	}
	return nil
}

func (h *ShareReferralHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *ShareReferralHandler) GetCategory() string {
	return h.category
}

// ShareEarningsChartHandler handles share earnings chart tasks
type ShareEarningsChartHandler struct {
	BaseTaskHandler
}

func NewShareEarningsChartHandler(service ActivityCashbackServiceInterface) *ShareEarningsChartHandler {
	return &ShareEarningsChartHandler{
		BaseTaskHandler: BaseTaskHandler{
			service:    service,
			identifier: model.TaskIDShareEarningsChart,
			category:   "community",
		},
	}
}

func (h *ShareEarningsChartHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	// Update user activity first
	if err := h.service.UpdateActivity(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to update user activity", zap.Error(err))
		// Continue with task completion even if UpdateActivity fails for backward compatibility
	}

	// Always allow task completion (increment progress)
	if err := h.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
		return fmt.Errorf("failed to increment share earnings chart progress: %w", err)
	}

	// Check if user has already received points today by checking daily completion history
	// We need to check if there's a daily completion record for today
	hasReceivedPointsToday, err := h.hasReceivedPointsToday(ctx, userID, task.ID)
	if err != nil {
		global.GVA_LOG.Error("Failed to check daily completion status", zap.Error(err))
		// Continue without awarding points to be safe
		return nil
	}

	// If not received points today, award points
	if !hasReceivedPointsToday {
		// Award points by creating a daily completion record
		verificationData := map[string]interface{}{
			"method":     "automated_share_action",
			"share_type": "earnings_chart",
		}

		// Create daily completion record to track that points were awarded today
		if err := h.createDailyCompletionRecord(ctx, userID, task.ID, task.Points, verificationData); err != nil {
			return fmt.Errorf("failed to create daily completion record: %w", err)
		}

		// Award points to user
		if err := h.service.AddPoints(ctx, userID, task.Points, "share_earnings_chart"); err != nil {
			return fmt.Errorf("failed to award points for share earnings chart: %w", err)
		}

		global.GVA_LOG.Info("Share earnings chart points awarded",
			zap.String("user_id", userID.String()),
			zap.Int("points", task.Points))
	} else {
		global.GVA_LOG.Info("Share earnings chart completed but points already awarded today",
			zap.String("user_id", userID.String()))
	}

	return nil
}

func (h *ShareEarningsChartHandler) GetIdentifier() model.TaskIdentifier {
	return h.identifier
}

func (h *ShareEarningsChartHandler) GetCategory() string {
	return h.category
}

// hasReceivedPointsToday checks if user has received points for this task today
func (h *ShareEarningsChartHandler) hasReceivedPointsToday(ctx context.Context, userID, taskID uuid.UUID) (bool, error) {
	// Create daily completion repository directly
	dailyRepo := activity_cashback.NewDailyTaskCompletionRepository()
	return dailyRepo.HasUserCompletedTaskToday(ctx, userID, taskID)
}

// createDailyCompletionRecord creates a daily completion record to track points awarded
func (h *ShareEarningsChartHandler) createDailyCompletionRecord(ctx context.Context, userID, taskID uuid.UUID, points int, verificationData map[string]interface{}) error {
	// Create daily completion repository directly
	dailyRepo := activity_cashback.NewDailyTaskCompletionRepository()

	// Create completion record
	now := time.Now()
	completion := &model.DailyTaskCompletion{
		UserID:         userID,
		TaskID:         taskID,
		PointsAwarded:  points,
		CompletionDate: now.Truncate(24 * time.Hour),
		CompletionTime: now,
	}

	// Set verification data
	completion.SetVerificationData("automated_share_action", "task_handler", verificationData)

	return dailyRepo.Create(ctx, completion)
}
