# Error Handling Implementation Guide

This document provides a comprehensive guide to implementing a robust error handling system based on the patterns used in the xbit-user-service project. The system is built on NestJS and GraphQL, with centralized error management, structured error codes, and comprehensive logging.

## Table of Contents

1. [Core Error Structure](#core-error-structure)
2. [Error Code Definition System](#error-code-definition-system)
3. [Error Classes and Types](#error-classes-and-types)
4. [Centralized Error Handling](#centralized-error-handling)
5. [Error Categories and Examples](#error-categories-and-examples)
6. [Best Practices](#best-practices)
7. [Implementation Steps](#implementation-steps)

## Core Error Structure

### IApiError Interface

The foundation of the error system is the `IApiError` interface:

```typescript
export interface IApiError {
    code: string;    // Unique error identifier (e.g., 'INVALID_GOOGLE_LOGIN')
    message: string; // User-friendly error message
}
```

### ApiError Class

The main error class that extends JavaScript's native Error:

```typescript
export class ApiError extends Error {
    public code: string;
    public message: string;

    constructor(error: IApiError) {
        super(`Error ${error.code}: ${error.message}`);
        this.code = error.code;
        this.message = error.message;
    }
}
```

### ServiceError Class

For external API call errors:

```typescript
export class ServiceError extends Error {
    constructor(url: string, statusCode: number, responseText: any) {
        console.error(`Call API ${url} error. Response code: ${statusCode}.
      Response text: ${JSON.stringify(responseText)}, status code: ${statusCode}.`);
        super('INTERNAL_SERVER_ERROR');
    }
}
```

## Error Code Definition System

### Error Constants Structure

All error codes are defined as constants in a centralized file (`errors.ts`):

```typescript
import { IApiError } from './index';
import { status } from '@grpc/grpc-js';

// Authentication Errors
export const INVALID_GOOGLE_LOGIN: IApiError = {
    code: 'INVALID_GOOGLE_LOGIN',
    message: 'Invalid Google login',
};

export const TOKEN_EXPIRED: IApiError = {
    code: 'TOKEN_EXPIRED',
    message: 'Token expired',
};

// Validation Errors
export const INVALID_EMAIL_FORMAT: IApiError = {
    code: 'INVALID_EMAIL_FORMAT',
    message: 'Invalid email format',
};

// Business Logic Errors
export const WALLET_NOT_FOUND: IApiError = {
    code: 'WALLET_NOT_FOUND',
    message: 'Wallet not found',
};

// Rate Limiting Errors
export const RATE_LIMIT_EXCEEDED: IApiError = {
    code: 'RATE_LIMIT_EXCEEDED',
    message: 'Too many requests from this IP address. Please try again later',
};
```

### GRPC Error Mapping

For gRPC services, errors are mapped to gRPC status codes:

```typescript
export const GRPC_EXCEPTION = {
    INTERNAL_SERVER_ERROR: {
        code: status.UNKNOWN,
        message: 'INTERNAL_SERVER_ERROR',
    },
    USER_NOT_FOUND: {
        code: status.FAILED_PRECONDITION,
        message: 'USER_NOT_FOUND',
    },
    INVALID_ARGUMENT: {
        code: status.INVALID_ARGUMENT,
        message: 'PLEASE_DEPOSIT_FIRST',
    },
};
```

## Error Classes and Types

### 1. ApiError (Primary)
- **Purpose**: Application-specific business logic errors
- **Usage**: `throw new ApiError(INVALID_GOOGLE_LOGIN)`
- **Characteristics**: Structured, user-friendly, trackable

### 2. ServiceError
- **Purpose**: External API communication errors
- **Usage**: Automatically thrown by BaseService on HTTP errors
- **Characteristics**: Includes URL, status code, and response data

### 3. HttpException (NestJS)
- **Purpose**: HTTP-specific errors with status codes
- **Usage**: `throw new HttpException('Unknown API error', HttpStatus.INTERNAL_SERVER_ERROR)`
- **Characteristics**: Maps to HTTP status codes

## Centralized Error Handling

### GraphQL Exception Filter

The primary error handler for GraphQL operations:

```typescript
@Catch()
export class GraphQLExceptionFilter implements GqlExceptionFilter {
    constructor(
        @InjectPinoLogger(GraphQLExceptionFilter.name)
        private readonly logger: PinoLogger,
    ) {}

    catch(exception: any, host: ArgumentsHost) {
        // Handle known GraphQL errors
        if (exception instanceof GraphQLError) {
            return exception;
        }

        // Handle NestJS HTTP exceptions
        if (exception instanceof HttpException) {
            return exception;
        }

        // Handle custom ApiError
        if (exception instanceof ApiError) {
            return new GraphQLError(exception.message, {
                extensions: {
                    code: exception.code,
                },
            });
        }

        // Log unknown errors
        this.logger.error(exception);

        // Return generic error for unknown exceptions
        return new GraphQLError('Internal server error', {
            extensions: {
                code: exception.code || 'INTERNAL_SERVER_ERROR',
            },
        });
    }
}
```

### Error Response Format

GraphQL errors are formatted consistently:

```typescript
formatError: (error) => {
    return {
        message: error.message,
        path: error.path,
        extensions: {
            code: error.extensions?.code || 'INTERNAL_SERVER_ERROR',
        },
    };
}
```

### Sentry Integration

Error monitoring with selective reporting:

```typescript
@Injectable()
export class SentryInterceptor implements NestInterceptor {
    private readonly EXCEPTION_IGNORE = [
        UnauthorizedException,
        BadRequestException,
        HealthCheckError,
        ApiError  // Don't report business logic errors
    ];

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        return next.handle().pipe(
            catchError((exception) => {
                if (process.env.STAGE !== 'local') {
                    const shouldIgnore = this.EXCEPTION_IGNORE.some(
                        (ignore) => exception instanceof ignore
                    );
                    if (!shouldIgnore) {
                        Sentry.captureException(exception);
                    }
                }
                throw exception;
            }),
        );
    }
}
```

## Error Categories and Examples

### 1. Authentication Errors
```typescript
// Token validation
if (!payload) {
    throw new ApiError(INVALID_GOOGLE_LOGIN);
}

// Token expiration
if (currentTime >= expTime) {
    throw new ApiError(TOKEN_EXPIRED);
}

// Signature validation
if (!this.verifyEVMSignature(data.message, data.signature, walletAddress)) {
    throw new ApiError(INVALID_SIGNATURE_ERROR);
}
```

### 2. Validation Errors
```typescript
// Email format validation
if (!this.validateEmail(input.email)) {
    throw new ApiError(INVALID_EMAIL_FORMAT);
}

// Input validation
if (!input.walletName || input.walletName.trim().length === 0) {
    throw new ApiError(INVALID_WALLET_NAME);
}
```

### 3. Business Logic Errors
```typescript
// Resource not found
const user = await this.userService.getUserById(userId);
if (!user) {
    throw new ApiError(USER_NOT_FOUND);
}

// Permission checks
if (wallet.userId !== userId) {
    throw new ApiError(WALLET_ACCESS_DENIED);
}

// Business rule violations
if (existingWallet) {
    throw new ApiError(WALLET_NAME_ALREADY_EXISTS);
}
```

### 4. Rate Limiting Errors
```typescript
// Context-aware rate limiting
const errorType = rateLimitOptions.errorType || 'GENERIC';
let error;
switch (errorType) {
    case 'EMAIL_OTP':
        error = new ApiError(EMAIL_OTP_RATE_LIMITED);
        break;
    case 'LOGIN':
        error = new ApiError(LOGIN_RATE_LIMITED);
        break;
    default:
        error = new ApiError(RATE_LIMIT_EXCEEDED);
        break;
}
throw error;
```

### 5. External Service Errors
```typescript
// API call error handling
try {
    const response = await axios({ method, url, data, ...config });
    return response.data;
} catch (error) {
    if (error.response) {
        throw new ServiceError(url, error.response.status, error.response.data);
    }
    throw new HttpException('Unknown API error', HttpStatus.INTERNAL_SERVER_ERROR);
}
```

## Best Practices

### 1. Error Code Naming Convention
- Use SCREAMING_SNAKE_CASE
- Be descriptive and specific
- Group by category (prefix): `INVALID_`, `FAILED_TO_`, `RATE_LIMIT_`

### 2. Error Message Guidelines
- Write user-friendly messages
- Avoid technical jargon
- Include actionable information when possible
- For rate limiting, specify wait times: "Please wait 15 minutes before trying again"

### 3. Error Propagation
```typescript
// Re-throw ApiError instances
if (error instanceof ApiError) {
    throw error;
}

// Convert unknown errors to ApiError
throw new ApiError(OPERATION_FAILED);
```

### 4. Logging Best Practices
```typescript
// Log with context
this.logger.warn('Rate limit exceeded', {
    ipAddress: this.hashIpForLogging(ipAddress),
    totalHits: result.totalHits,
    maxRequests: config.maxRequests,
    resetTime: new Date(result.resetTime).toISOString(),
});

// Log error details for debugging
this.logger.warn('Failed to initialize OTP authentication', {
    error: {
        message: error?.message || 'Unknown error',
        stack: error?.stack,
        code: error?.code,
        name: error?.name,
    },
    input,
});
```

### 5. Error Recovery Patterns
```typescript
// Retry logic with error handling
try {
    return await this.performOperation();
} catch (error: any) {
    if (error instanceof ApiError) {
        throw error; // Don't retry business logic errors
    }

    if (retry_time > MAX_RETRIES) {
        this.logger.error({ retry_time }, 'Max retry attempts reached');
        throw new HttpException('Operation failed', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // Retry for transient errors
    return this.performOperationWithRetry(retry_time + 1);
}
```

## Implementation Steps

### Step 1: Create Core Error Infrastructure

1. **Create the error interface and classes**:
```typescript
// libs/common/api-errors/index.ts
export interface IApiError {
    code: string;
    message: string;
}

export class ApiError extends Error {
    public code: string;
    public message: string;

    constructor(error: IApiError) {
        super(`Error ${error.code}: ${error.message}`);
        this.code = error.code;
        this.message = error.message;
    }
}

export class ServiceError extends Error {
    constructor(url: string, statusCode: number, responseText: any) {
        console.error(`Call API ${url} error. Response code: ${statusCode}.
      Response text: ${JSON.stringify(responseText)}, status code: ${statusCode}.`);
        super('INTERNAL_SERVER_ERROR');
    }
}
```

2. **Define error constants**:
```typescript
// libs/common/api-errors/errors.ts
import { IApiError } from './index';

export const INVALID_INPUT: IApiError = {
    code: 'INVALID_INPUT',
    message: 'Invalid input provided',
};

export const USER_NOT_FOUND: IApiError = {
    code: 'USER_NOT_FOUND',
    message: 'User not found',
};

export const UNAUTHORIZED_ACCESS: IApiError = {
    code: 'UNAUTHORIZED_ACCESS',
    message: 'Unauthorized access',
};

export const RATE_LIMIT_EXCEEDED: IApiError = {
    code: 'RATE_LIMIT_EXCEEDED',
    message: 'Too many requests. Please try again later',
};

export const INTERNAL_SERVER_ERROR: IApiError = {
    code: 'INTERNAL_SERVER_ERROR',
    message: 'Internal server error occurred',
};
```

### Step 2: Implement Exception Filters

1. **GraphQL Exception Filter**:
```typescript
// libs/common/filters/graphql-exception.filter.ts
import { Catch, ArgumentsHost, HttpException } from '@nestjs/common';
import { GqlExceptionFilter, GqlArgumentsHost } from '@nestjs/graphql';
import { GraphQLError } from 'graphql';
import { ApiError } from '../api-errors';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Catch()
export class GraphQLExceptionFilter implements GqlExceptionFilter {
    constructor(
        @InjectPinoLogger(GraphQLExceptionFilter.name)
        private readonly logger: PinoLogger,
    ) {}

    catch(exception: any, host: ArgumentsHost) {
        if (exception instanceof GraphQLError) {
            return exception;
        }

        if (exception instanceof HttpException) {
            return exception;
        }

        if (exception instanceof ApiError) {
            return new GraphQLError(exception.message, {
                extensions: { code: exception.code },
            });
        }

        this.logger.error(exception);
        return new GraphQLError('Internal server error', {
            extensions: { code: 'INTERNAL_SERVER_ERROR' },
        });
    }
}
```

2. **HTTP Exception Filter** (for REST APIs):
```typescript
// libs/common/filters/http-exception.filter.ts
import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiError } from '../api-errors';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
    constructor(
        @InjectPinoLogger(HttpExceptionFilter.name)
        private readonly logger: PinoLogger,
    ) {}

    catch(exception: unknown, host: ArgumentsHost): void {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();

        let status = HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        let code = 'INTERNAL_SERVER_ERROR';

        if (exception instanceof HttpException) {
            status = exception.getStatus();
            message = exception.message;
        } else if (exception instanceof ApiError) {
            status = this.mapErrorCodeToHttpStatus(exception.code);
            message = exception.message;
            code = exception.code;
        } else {
            this.logger.error(exception);
        }

        response.status(status).json({
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: request.url,
            error: {
                code,
                message,
            },
        });
    }

    private mapErrorCodeToHttpStatus(errorCode: string): number {
        const statusMap: Record<string, number> = {
            'INVALID_INPUT': HttpStatus.BAD_REQUEST,
            'UNAUTHORIZED_ACCESS': HttpStatus.UNAUTHORIZED,
            'USER_NOT_FOUND': HttpStatus.NOT_FOUND,
            'RATE_LIMIT_EXCEEDED': HttpStatus.TOO_MANY_REQUESTS,
            'FORBIDDEN_OPERATION': HttpStatus.FORBIDDEN,
        };

        return statusMap[errorCode] || HttpStatus.INTERNAL_SERVER_ERROR;
    }
}
```

### Step 3: Set Up Error Monitoring

1. **Sentry Interceptor**:
```typescript
// libs/sentry/src/sentry.interceptor.ts
import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import * as Sentry from '@sentry/nestjs';
import { ApiError } from 'libs/common/api-errors';

@Injectable()
export class SentryInterceptor implements NestInterceptor {
    private readonly EXCEPTION_IGNORE = [ApiError]; // Don't report business logic errors

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        return next.handle().pipe(
            catchError((exception) => {
                if (process.env.NODE_ENV !== 'development') {
                    const shouldIgnore = this.EXCEPTION_IGNORE.some(
                        (ignore) => exception instanceof ignore
                    );
                    if (!shouldIgnore) {
                        Sentry.captureException(exception);
                    }
                }
                throw exception;
            }),
        );
    }
}
```

### Step 4: Application Setup

1. **Register global filters and interceptors**:
```typescript
// main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { GraphQLExceptionFilter } from './common/filters/graphql-exception.filter';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { SentryInterceptor } from './sentry/sentry.interceptor';
import { PinoLogger } from 'nestjs-pino';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);

    const logger = await app.resolve(PinoLogger);

    // Register global filters
    app.useGlobalFilters(new GraphQLExceptionFilter(logger));
    app.useGlobalFilters(new HttpExceptionFilter(logger));

    // Register global interceptors
    app.useGlobalInterceptors(new SentryInterceptor());

    await app.listen(3000);
}
bootstrap();
```

2. **GraphQL configuration**:
```typescript
// app.module.ts
GraphQLModule.forRoot<ApolloDriverConfig>({
    driver: ApolloDriver,
    autoSchemaFile: true,
    formatError: (error) => {
        return {
            message: error.message,
            path: error.path,
            extensions: {
                code: error.extensions?.code || 'INTERNAL_SERVER_ERROR',
            },
        };
    },
}),
```

### Step 5: Usage Patterns

1. **In Services**:
```typescript
// user.service.ts
import { Injectable } from '@nestjs/common';
import { ApiError } from '../common/api-errors';
import { USER_NOT_FOUND, INVALID_INPUT } from '../common/api-errors/errors';

@Injectable()
export class UserService {
    async getUserById(id: string) {
        if (!id || id.trim().length === 0) {
            throw new ApiError(INVALID_INPUT);
        }

        const user = await this.userRepository.findById(id);
        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        return user;
    }
}
```

2. **In Resolvers/Controllers**:
```typescript
// user.resolver.ts
import { Resolver, Query, Args } from '@nestjs/graphql';
import { UserService } from './user.service';

@Resolver()
export class UserResolver {
    constructor(private readonly userService: UserService) {}

    @Query(() => User)
    async getUser(@Args('id') id: string) {
        // Service will throw ApiError if user not found
        // Exception filter will handle the error automatically
        return this.userService.getUserById(id);
    }
}
```

## Error Testing Strategy

### Unit Testing Error Scenarios

```typescript
// user.service.spec.ts
describe('UserService', () => {
    it('should throw USER_NOT_FOUND when user does not exist', async () => {
        jest.spyOn(userRepository, 'findById').mockResolvedValue(null);

        await expect(userService.getUserById('invalid-id'))
            .rejects
            .toThrow(ApiError);

        await expect(userService.getUserById('invalid-id'))
            .rejects
            .toMatchObject({
                code: 'USER_NOT_FOUND',
                message: 'User not found'
            });
    });
});
```

### Integration Testing

```typescript
// user.resolver.integration.spec.ts
describe('UserResolver Integration', () => {
    it('should return proper GraphQL error for non-existent user', async () => {
        const query = `
            query GetUser($id: String!) {
                getUser(id: $id) {
                    id
                    name
                }
            }
        `;

        const response = await request(app.getHttpServer())
            .post('/graphql')
            .send({ query, variables: { id: 'non-existent' } });

        expect(response.body.errors).toHaveLength(1);
        expect(response.body.errors[0]).toMatchObject({
            message: 'User not found',
            extensions: {
                code: 'USER_NOT_FOUND'
            }
        });
    });
});
```

## Monitoring and Alerting

### Error Metrics

1. **Error Rate Monitoring**: Track error rates by error code
2. **Response Time Impact**: Monitor how errors affect response times
3. **User Impact**: Track which errors affect user experience most

### Logging Strategy

```typescript
// Enhanced logging with context
this.logger.error('Operation failed', {
    errorCode: error.code,
    userId: context.userId,
    operation: 'getUserById',
    input: { id },
    timestamp: new Date().toISOString(),
    requestId: context.requestId,
});
```

This comprehensive error handling system provides:
- **Consistency**: Uniform error structure across the application
- **Maintainability**: Centralized error definitions and handling
- **Observability**: Comprehensive logging and monitoring
- **User Experience**: Clear, actionable error messages
- **Developer Experience**: Easy to use and extend error system
